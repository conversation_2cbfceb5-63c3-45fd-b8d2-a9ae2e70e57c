You have an existing RAG-based chatbot for the openBIS documentation using Python, LangGraph, and Ollama. You want to enhance it so it can also execute functions from the pybis Python library. The chatbot needs to be smart enough to decide whether a user's query requires retrieving information from the documentation (the current RAG functionality) or performing an action by calling a pybis function. If it decides to call a function, it needs to identify the correct function and extract the necessary parameters from the user's input. You're open to transforming it into a multi-agent project and want a detailed workflow.
This is a classic "Tool Use" or "Function Calling" pattern in conversational AI, combined with your existing RAG capability. A multi-agent architecture is indeed a suitable and robust way to implement this, especially with LangGraph.
Here’s a proposed multi-agent architecture and a step-by-step workflow to achieve this:
Proposed Multi-Agent Architecture
We can structure this using LangGraph nodes representing different responsibilities:
Router/Orchestrator Agent: This is the central decision-maker. It takes the user's input and decides which other agent or process should handle the request.
Possible decisions:
Needs RAG (documentation query).
Needs pybis Function Call (action request).
Simple conversational response (e.g., greetings, clarification needed).
RAG Agent (Existing functionality): This agent is responsible for handling documentation-based questions. It will use your existing RAG logic (retrieval, context building, LLM generation based on context).
Function Calling Agent (New): This agent is responsible for executing actions via the pybis library. Its internal process will involve:
Tool Selection: Given the user's intent (routed by the Orchestrator), identify the specific pybis function (Tool) to call.
Parameter Extraction: Parse the user's input to get the arguments required for the selected function.
Tool Execution: Call the actual pybis function with the extracted parameters.
Result Interpretation: Process the output from the pybis function and format it into a natural language response for the user.
Response Formatting Agent: A final step to take the output from either the RAG Agent or the Function Calling Agent (or a simple conversational response) and format it into the final message sent back to the user.
LangGraph State and Workflow
Your LangGraph StateGraph will need to manage the flow between these agents.
State: Define the state TypedDict to hold information as it passes through the graph, e.g.:
from typing import TypedDict, List, Any
from langchain_core.messages import BaseMessage

class ConversationState(TypedDict):
    user_query: str  # The original input
    chat_history: List[BaseMessage] # Previous turns for context
    decision: str # What the Router decided ("rag", "function_call", "converse")
    rag_context: List[str] # Retrieved documentation chunks
    rag_answer: str # Answer generated by RAG agent
    tool_action: Any # Action object if a tool needs to be called (e.g., LangChain's AgentAction)
    tool_output: Any # Result of the tool execution
    final_response: str # The message sent back to the user
    session_id: str # To maintain conversation history persistence
Use code with caution.
Python
Nodes:
user_input: Receives the user query and updates user_query and chat_history. (Initial node)
router: Implements the logic of the Router Agent. Takes user_query and chat_history, decides decision ("rag", "function_call", "converse").
rag_agent: Implements the RAG Agent. Takes user_query and chat_history, performs RAG, sets rag_context and rag_answer.
function_calling_agent: Implements the Function Calling Agent. Takes user_query and chat_history. Uses an LLM capable of tool use to determine tool_action and tool_output (via internal execution logic).
format_response: Takes the result from the previous step (rag_answer, tool_output, or a simple final_response if routed to "converse") and formats it nicely. Updates final_response.
Edges:
Initial edge: user_input -> router
Conditional edge from router:
If decision is "rag": -> rag_agent
If decision is "function_call": -> function_calling_agent
If decision is "converse": -> format_response (or a dedicated simple response node first)
Edges from rag_agent and function_calling_agent: -> format_response
End node: format_response (or loop back to user_input for next turn after saving state).
Detailed Workflow Steps
Here's a step-by-step plan to implement this:
Identify & Wrap pybis Functions as Tools:
Action: Go through the pybis API and identify the functions you want the chatbot to be able to execute (e.g., creating datasets, listing data, getting metadata). Start small! Don't try to wrap the whole API at once.
Action: Create Python functions that wrap these pybis calls. These wrapper functions should:
Handle the pybis connection setup/teardown (or reuse a connection if stateful).
Accept parameters that can be easily extracted from natural language (e.g., dataset_name: str, experiment_id: str).
Include clear, concise docstrings describing what the function does and what parameters it needs. This is CRUCIAL because the LLM will use these descriptions to decide if and how to call the function.
Return their output in a format that's easy for the LLM (or a subsequent step) to interpret and summarize (e.g., a dictionary, a formatted string).
Action: Use LangChain's Tool concept. You can often convert simple Python functions with good docstrings into Tool objects easily. LangChain's function-calling capabilities (especially when paired with models that support it, like many large Ollama models) rely on this.
Refactor Existing RAG Logic:
Action: Isolate the logic within your current ConversationEngine that specifically handles RAG queries (retrieving context, generating the RAG answer).
Action: Package this logic into a distinct function or class method that can act as the rag_agent node in the new LangGraph structure. It should take the ConversationState (or relevant parts like user_query, chat_history) and update the state with rag_context and rag_answer.
Implement the Function Calling Agent:
Action: Create the logic for the function_calling_agent node. This will be the most complex new part.
Action: Within this node, use the LLM specifically for tool selection and parameter extraction. Provide the LLM with the list of Tool objects created in Step 1. Many LLMs (especially those tuned for function calling) can process the user query and the tool descriptions and output a structured call (e.g., JSON) indicating which tool to use and what arguments to pass. LangChain provides runnables/agents that handle this interaction pattern.
Action: Implement the execution part. Once the LLM suggests a tool and parameters, validate them and call the corresponding Python wrapper function created in Step 1.
Action: Implement the result interpretation. Take the output from the executed function (tool_output) and potentially pass it back to the LLM to generate a user-friendly summary or confirmation message. Update the state with the result.
Develop the Router/Orchestrator Agent:
Action: Create the logic for the router node. This node receives the user query and conversation history.
Action: Use the LLM (or a simpler classification model if appropriate, though an LLM is often required to understand nuances) to decide the user's primary intent. Prompt the LLM to classify the query as needing "documentation lookup", "perform an action", or "general conversation". Based on the LLM's output, set the decision state variable.
Alternatively (and often integrated into Tool Calling frameworks): The router is the LLM that is given both the RAG process (represented as a conceptual "Answer questions about documentation" tool) and the pybis functions (as explicit Tools). The LLM then directly decides which "tool" to use, and the LangGraph edges route based on the tool it picked. This simplifies the separate "router" node slightly by merging the routing decision into the first LLM call.
Build the LangGraph State Machine:
Action: Define the StateGraph using the ConversationState and the nodes (user_input, router, rag_agent, function_calling_agent, format_response).
Action: Define the edges, especially the conditional edges based on the decision output from the router.
Action: Add persistence for the chat_history and potentially other parts of the state using LangGraph's checkpointing (which you seem to be using with SQLite).
Integrate into Interfaces:
Action: Update the Web (web/app.py) and CLI (query/cli.py) interfaces to use the new LangGraph structure. The core interaction loop (conversation_engine.chat) will now run through this more complex graph.
Action: Ensure the interfaces can handle the different types of final responses returned by the format_response node (e.g., a text answer from RAG, a confirmation message and result from a function call).
Testing and Refinement:
Action: Test thoroughly. Test documentation questions, simple function calls (e.g., list datasets), function calls with parameters (e.g., create dataset X), ambiguous queries, queries that combine RAG and function calling (initially the router might struggle here), and error cases (e.g., trying to create a dataset that exists, calling a function with missing parameters).
Action: Refine the tool descriptions (docstrings). This is key to the LLM making the right decisions.
Action: Refine the prompts used in the router and function_calling_agent to guide the LLM's behavior.
Action: Add robust error handling within the tool wrapper functions and the function_calling_agent node.
Example Workflow Trace (User wants to create a dataset)
User Input: "Can you create a new dataset called 'MyExperimentData'?"
user_input Node: Receives query, updates user_query in state.
router Node:
Receives user_query.
Uses LLM (or internal logic) to analyze: "create a new dataset" sounds like an action.
Sets decision state variable to "function_call".
Conditional Edge: Based on decision="function_call", routes to function_calling_agent.
function_calling_agent Node:
Receives state.
Tool Selection: LLM (given tool descriptions) determines the user wants to use the create_dataset tool/function. Sets tool_action.
Parameter Extraction: LLM parses "called 'MyExperimentData'" and extracts dataset_name="MyExperimentData". Adds parameters to tool_action.
Tool Execution: The node code calls the actual Python create_dataset("MyExperimentData") wrapper function.
Result Interpretation: The wrapper function returns success/failure message or object. This is put into tool_output.
Edge: Routes to format_response.
format_response Node:
Receives state, sees tool_output is present.
Formats a user-friendly message based on the tool_output (e.g., "Successfully created dataset 'MyExperimentData'." or "Error creating dataset: ..."). Sets final_response.
End: The final_response is sent back to the user via the interface. The conversation state is saved (checkpointed).
Key Considerations
LLM Capability: Ensure the specific Ollama model you are using (qwen3) has good function calling capabilities. You might need to experiment with different models if qwen3 struggles.
Parameter Ambiguity: Real-world user queries might be vague. "Create dataset" - what kind? Where? The Function Calling Agent might need to handle cases where parameters are missing, possibly by asking follow-up questions (this adds complexity to the LangGraph state and flow).
pybis Connection/Authentication: How will the pybis functions authenticate with openBIS? Will the user provide credentials? Will there be a standing connection? Handle this securely.
Security: Carefully consider which pybis functions you expose. Executing actions based on user input requires caution. Limit the available tools to safe and necessary operations.
Error Handling: Implement robust try...except blocks around the pybis calls and parameter parsing. The chatbot needs to gracefully handle API errors, invalid parameters, etc., and inform the user clearly.
Complexity: Adding function calling significantly increases complexity compared to pure RAG. Start with a very small set of functions (pybis tools) and gradually expand.
This plan outlines the necessary architectural changes and steps to add pybis function execution capabilities to your openBIS chatbot using a multi-agent approach within LangGraph. Good luck!


In this website there are all the different pybis functions that can be executed and some explanations about how to use them.

https://pypi.org/project/pybis/

So create the tools for every possible pybis function, the chatbot will need to know through the agent how to execute those functions (which parameters, when to call each of the functions, etc). For that, you will need to implement very good, clear and concise docstrings, describing what the function does and what parameters it needs. This is CRUCIAL because the LLM will use these descriptions to decide if and how to call the function. For doing so, use the website to get this information